"use client";

import React, { createContext, useContext, useState, useEffect } from 'react';
import { logger } from '@/lib/logger';

export type CookieCategory = 'essential' | 'functional' | 'analytics';

export interface CookieConsent {
  essential: boolean;
  functional: boolean;
  analytics: boolean;
  timestamp: number;
  version: string;
}

interface CookieConsentContextType {
  consent: CookieConsent | null;
  hasConsented: boolean;
  updateConsent: (newConsent: Partial<CookieConsent>) => void;
  acceptAll: () => void;
  rejectAll: () => void;
  showBanner: boolean;
  hideBanner: () => void;
  showSettings: () => void;
}

const CookieConsentContext = createContext<CookieConsentContextType | undefined>(undefined);

const CONSENT_COOKIE_NAME = 'flash-cards-ai-consent';
const CONSENT_VERSION = '1.0';
const CONSENT_EXPIRY_DAYS = 365;

const DEFAULT_CONSENT: CookieConsent = {
  essential: true, // Always true - required for app functionality
  functional: false,
  analytics: false,
  timestamp: Date.now(),
  version: CONSENT_VERSION
};

function getStoredConsent(): CookieConsent | null {
  if (typeof window === 'undefined') return null;
  
  try {
    const stored = localStorage.getItem(CONSENT_COOKIE_NAME);
    if (!stored) return null;
    
    const parsed = JSON.parse(stored);
    
    // Check if consent is still valid (version and expiry)
    const isExpired = Date.now() - parsed.timestamp > (CONSENT_EXPIRY_DAYS * 24 * 60 * 60 * 1000);
    const isOldVersion = parsed.version !== CONSENT_VERSION;
    
    if (isExpired || isOldVersion) {
      localStorage.removeItem(CONSENT_COOKIE_NAME);
      return null;
    }
    
    return parsed;
  } catch (error) {
    logger.error('Error reading stored consent:', error);
    localStorage.removeItem(CONSENT_COOKIE_NAME);
    return null;
  }
}

function storeConsent(consent: CookieConsent): void {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.setItem(CONSENT_COOKIE_NAME, JSON.stringify(consent));
    
    // Also set a cookie for server-side access if needed
    const expiryDate = new Date();
    expiryDate.setDate(expiryDate.getDate() + CONSENT_EXPIRY_DAYS);
    
    document.cookie = `${CONSENT_COOKIE_NAME}=${JSON.stringify(consent)}; expires=${expiryDate.toUTCString()}; path=/; SameSite=Lax`;
    
    logger.log('Cookie consent stored:', {
      essential: consent.essential,
      functional: consent.functional,
      analytics: consent.analytics,
      version: consent.version
    });
  } catch (error) {
    logger.error('Error storing consent:', error);
  }
}

export function CookieConsentProvider({ children }: { children: React.ReactNode }) {
  const [consent, setConsent] = useState<CookieConsent | null>(null);
  const [showBanner, setShowBanner] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    const storedConsent = getStoredConsent();
    
    if (storedConsent) {
      setConsent(storedConsent);
      setShowBanner(false);
    } else {
      setShowBanner(true);
    }
    
    setIsLoaded(true);
  }, []);

  const updateConsent = (newConsent: Partial<CookieConsent>) => {
    const updatedConsent: CookieConsent = {
      ...DEFAULT_CONSENT,
      ...consent,
      ...newConsent,
      essential: true, // Always true
      timestamp: Date.now(),
      version: CONSENT_VERSION
    };
    
    setConsent(updatedConsent);
    storeConsent(updatedConsent);
    setShowBanner(false);
    
    // Trigger analytics reload if consent changed
    if (newConsent.analytics !== undefined) {
      window.dispatchEvent(new CustomEvent('cookieConsentChanged', {
        detail: { analytics: updatedConsent.analytics }
      }));
    }
  };

  const acceptAll = () => {
    updateConsent({
      functional: true,
      analytics: true
    });
  };

  const rejectAll = () => {
    updateConsent({
      functional: false,
      analytics: false
    });
  };

  const hideBanner = () => {
    setShowBanner(false);
  };

  const showSettings = () => {
    setShowBanner(true);
  };

  const hasConsented = consent !== null;

  const value: CookieConsentContextType = {
    consent,
    hasConsented,
    updateConsent,
    acceptAll,
    rejectAll,
    showBanner: showBanner && isLoaded,
    hideBanner,
    showSettings
  };

  return (
    <CookieConsentContext.Provider value={value}>
      {children}
    </CookieConsentContext.Provider>
  );
}

export function useCookieConsent() {
  const context = useContext(CookieConsentContext);
  if (context === undefined) {
    throw new Error('useCookieConsent must be used within a CookieConsentProvider');
  }
  return context;
}

// Utility functions for checking specific consent
export function hasAnalyticsConsent(): boolean {
  if (typeof window === 'undefined') return false;
  
  const stored = getStoredConsent();
  return stored?.analytics ?? false;
}

export function hasFunctionalConsent(): boolean {
  if (typeof window === 'undefined') return false;
  
  const stored = getStoredConsent();
  return stored?.functional ?? false;
}

// Cookie categories information for the consent banner
export const COOKIE_CATEGORIES = {
  essential: {
    name: 'Essential Cookies',
    description: 'Required for the website to function properly. These cannot be disabled.',
    cookies: ['Authentication tokens', 'Session management', 'Security features'],
    required: true
  },
  functional: {
    name: 'Functional Cookies',
    description: 'Remember your preferences and settings to improve your experience.',
    cookies: ['Sidebar state', 'Theme preferences', 'Language settings'],
    required: false
  },
  analytics: {
    name: 'Analytics Cookies',
    description: 'Help us understand how you use our website to improve performance.',
    cookies: ['Google Analytics', 'Vercel Analytics', 'Performance monitoring'],
    required: false
  }
} as const;
