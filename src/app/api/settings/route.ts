import { NextRequest, NextResponse } from 'next/server';
import { getUserTierServer, isFirebaseAdminConfigured } from '@/lib/firebase-admin';
import { updateUserSettingsServer, getUserSettingsServer } from '@/lib/settings-service-server';
import type { UserSettings } from '@/types';
import { logger } from '@/lib/logger';

interface UpdateSettingsRequest {
  userId: string;
  settings: Partial<UserSettings>;
}

interface GetSettingsRequest {
  userId: string;
}

/**
 * GET /api/settings - Get user settings
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    // Check if Firebase Admin is properly configured
    if (!isFirebaseAdminConfigured()) {
      logger.error('[Settings API] Firebase Admin not configured - missing service account credentials');
      return NextResponse.json(
        {
          success: false,
          error: 'Settings service is currently unavailable. Please contact support.',
          code: 'SERVICE_UNAVAILABLE'
        },
        { status: 503 }
      );
    }

    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'User ID is required' },
        { status: 400 }
      );
    }

    logger.log('[Settings API] Getting settings for user:', userId);

    const settings = await getUserSettingsServer(userId);

    return NextResponse.json({
      success: true,
      settings
    });

  } catch (error) {
    logger.error('[Settings API] Error getting settings:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to get settings' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/settings - Update user settings
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Check if Firebase Admin is properly configured
    if (!isFirebaseAdminConfigured()) {
      logger.error('[Settings API] Firebase Admin not configured - missing service account credentials');
      return NextResponse.json(
        {
          success: false,
          error: 'Settings service is currently unavailable. Please contact support.',
          code: 'SERVICE_UNAVAILABLE'
        },
        { status: 503 }
      );
    }

    const body: UpdateSettingsRequest = await request.json();
    const { userId, settings } = body;

    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'User ID is required' },
        { status: 400 }
      );
    }

    if (!settings || typeof settings !== 'object') {
      return NextResponse.json(
        { success: false, error: 'Settings object is required' },
        { status: 400 }
      );
    }

    logger.log('[Settings API] Updating settings for user:', userId);
    logger.log('[Settings API] Settings update:', {
      ...settings,
      elevenLabsApiKey: settings.elevenLabsApiKey ? '[REDACTED]' : settings.elevenLabsApiKey
    });
    logger.log('[Settings API] API key details:', {
      hasApiKey: 'elevenLabsApiKey' in settings,
      apiKeyValue: settings.elevenLabsApiKey,
      apiKeyType: typeof settings.elevenLabsApiKey,
      apiKeyLength: settings.elevenLabsApiKey ? settings.elevenLabsApiKey.length : 0
    });

    // Validate that user exists and get their tier for logging
    try {
      const userTier = await getUserTierServer(userId);
      logger.log('[Settings API] User tier:', userTier);
    } catch (error) {
      logger.error('[Settings API] Error getting user tier:', error);
      return NextResponse.json(
        { success: false, error: 'User not found or invalid' },
        { status: 404 }
      );
    }

    // Update settings using server-side service (handles encryption)
    const result = await updateUserSettingsServer(userId, settings);

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error || 'Failed to update settings' },
        { status: 400 }
      );
    }

    logger.log('[Settings API] Settings updated successfully');

    return NextResponse.json({
      success: true,
      message: 'Settings updated successfully'
    });

  } catch (error) {
    logger.error('[Settings API] Error updating settings:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update settings' },
      { status: 500 }
    );
  }
}
