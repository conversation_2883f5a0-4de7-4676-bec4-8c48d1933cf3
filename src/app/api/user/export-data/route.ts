import { NextRequest, NextResponse } from 'next/server';
import { adminDb, isFirebaseAdminConfigured } from '@/lib/firebase-admin';
import { logger } from '@/lib/logger';
import { decryptApiKey } from '@/lib/api-key-utils';

interface ExportedUserData {
  exportInfo: {
    exportedAt: string;
    userId: string;
    version: string;
    format: string;
  };
  profile: {
    userId: string;
    email?: string;
    createdAt?: string;
    lastLoginAt?: string;
  };
  settings: {
    answerDetail?: number;
    cardRange?: {
      min: number;
      max: number;
    };
    ttsVoices?: {
      questionVoice: string;
      answerVoice: string;
    };
    hasCustomApiKey?: boolean; // Don't export the actual key for security
  };
  flashcardSets: Array<{
    id: string;
    title: string;
    createdAt: string;
    flashcardCount: number;
    flashcards: Array<{
      question: string;
      answer: string;
      id?: number;
    }>;
  }>;
  usage: {
    tier?: string;
    dailyUploads?: number;
    monthlyUploads?: number;
    lastUploadAt?: string;
    lastResetAt?: string;
    lastUpdatedAt?: string;
  };
  subscription?: {
    tier: string;
    status?: string;
    // Note: Payment details are not included for security reasons
  };
}

// Rate limiting: Track export requests per user
const exportRequestTracker = new Map<string, { count: number; lastRequest: number }>();
const RATE_LIMIT_WINDOW = 60 * 60 * 1000; // 1 hour
const MAX_EXPORTS_PER_HOUR = 3;

function checkRateLimit(userId: string): { allowed: boolean; resetTime?: number } {
  const now = Date.now();
  const userRequests = exportRequestTracker.get(userId);

  if (!userRequests) {
    exportRequestTracker.set(userId, { count: 1, lastRequest: now });
    return { allowed: true };
  }

  // Reset if window has passed
  if (now - userRequests.lastRequest > RATE_LIMIT_WINDOW) {
    exportRequestTracker.set(userId, { count: 1, lastRequest: now });
    return { allowed: true };
  }

  // Check if limit exceeded
  if (userRequests.count >= MAX_EXPORTS_PER_HOUR) {
    const resetTime = userRequests.lastRequest + RATE_LIMIT_WINDOW;
    return { allowed: false, resetTime };
  }

  // Increment count
  userRequests.count++;
  userRequests.lastRequest = now;
  exportRequestTracker.set(userId, userRequests);
  
  return { allowed: true };
}

/**
 * POST /api/user/export-data - Export all user data
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Check if Firebase Admin is properly configured
    if (!isFirebaseAdminConfigured()) {
      logger.error('[Data Export] Firebase Admin not configured - missing service account credentials');
      return NextResponse.json(
        {
          success: false,
          error: 'Data export service is currently unavailable. Please contact support.',
          code: 'SERVICE_UNAVAILABLE'
        },
        { status: 503 }
      );
    }

    const body = await request.json();
    const { userId } = body;

    if (!userId || typeof userId !== 'string') {
      return NextResponse.json(
        { success: false, error: 'Valid user ID is required' },
        { status: 400 }
      );
    }

    logger.log('[Data Export] Export request for user:', userId);

    // Check rate limiting
    const rateLimitCheck = checkRateLimit(userId);
    if (!rateLimitCheck.allowed) {
      const resetTime = new Date(rateLimitCheck.resetTime!).toISOString();
      logger.warn('[Data Export] Rate limit exceeded for user:', userId);
      return NextResponse.json(
        { 
          success: false, 
          error: 'Export rate limit exceeded. Please try again later.',
          resetTime 
        },
        { status: 429 }
      );
    }

    // Initialize export data structure
    const exportData: ExportedUserData = {
      exportInfo: {
        exportedAt: new Date().toISOString(),
        userId,
        version: '1.0',
        format: 'JSON'
      },
      profile: {
        userId
      },
      settings: {},
      flashcardSets: [],
      usage: {}
    };

    // Collect user profile data
    try {
      const userDoc = await adminDb.collection('users').doc(userId).get();
      if (userDoc.exists) {
        const userData = userDoc.data();
        exportData.profile = {
          userId,
          email: userData?.email,
          createdAt: userData?.createdAt,
          lastLoginAt: userData?.lastLoginAt
        };

        // Export settings (excluding sensitive data)
        if (userData?.settings) {
          exportData.settings = {
            answerDetail: userData.settings.answerDetail,
            cardRange: userData.settings.cardRange,
            ttsVoices: userData.settings.ttsVoices,
            hasCustomApiKey: !!userData.settings.elevenLabsApiKey
          };
        }
      }
    } catch (error) {
      logger.error('[Data Export] Error fetching user profile:', error);
      // Continue with export even if profile fetch fails
    }

    // Collect flashcard sets
    try {
      const flashcardsSnapshot = await adminDb
        .collection('flashcardSets')
        .where('userId', '==', userId)
        .orderBy('createdAt', 'desc')
        .get();

      exportData.flashcardSets = flashcardsSnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          title: data.title,
          createdAt: data.createdAt?.toDate?.()?.toISOString() || data.createdAt,
          flashcardCount: data.flashcards?.length || 0,
          flashcards: data.flashcards || []
        };
      });
    } catch (error) {
      logger.error('[Data Export] Error fetching flashcard sets:', error);
      // Continue with export even if flashcards fetch fails
    }

    // Collect usage data
    try {
      const usageDoc = await adminDb.collection('userUsage').doc(userId).get();
      if (usageDoc.exists) {
        const usageData = usageDoc.data();
        exportData.usage = {
          tier: usageData?.tier,
          dailyUploads: usageData?.dailyUploads,
          monthlyUploads: usageData?.monthlyUploads,
          lastUploadAt: usageData?.lastUploadAt?.toDate?.()?.toISOString() || usageData?.lastUploadAt,
          lastResetAt: usageData?.lastResetAt?.toDate?.()?.toISOString() || usageData?.lastResetAt,
          lastUpdatedAt: usageData?.lastUpdatedAt?.toDate?.()?.toISOString() || usageData?.lastUpdatedAt
        };
      }
    } catch (error) {
      logger.error('[Data Export] Error fetching usage data:', error);
      // Continue with export even if usage fetch fails
    }

    // Collect subscription data (basic info only, no payment details)
    try {
      const subscriptionSnapshot = await adminDb
        .collection('subscriptions')
        .where('userId', '==', userId)
        .limit(1)
        .get();

      if (!subscriptionSnapshot.empty) {
        const subscriptionData = subscriptionSnapshot.docs[0].data();
        exportData.subscription = {
          tier: subscriptionData.tier || exportData.usage.tier || 'FREE',
          status: subscriptionData.status
        };
      }
    } catch (error) {
      logger.error('[Data Export] Error fetching subscription data:', error);
      // Continue with export even if subscription fetch fails
    }

    // Log successful export (without sensitive data)
    logger.log('[Data Export] Export completed for user:', {
      userId,
      flashcardSetsCount: exportData.flashcardSets.length,
      totalFlashcards: exportData.flashcardSets.reduce((sum, set) => sum + set.flashcardCount, 0),
      tier: exportData.usage.tier || 'FREE'
    });

    // Return the exported data
    return NextResponse.json({
      success: true,
      data: exportData,
      message: 'Data export completed successfully'
    });

  } catch (error) {
    logger.error('[Data Export] Error during export:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to export user data' },
      { status: 500 }
    );
  }
}
