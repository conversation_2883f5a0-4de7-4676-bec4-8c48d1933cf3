"use client";

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog';
import { <PERSON>ie, Settings, Shield, BarChart3, Wrench } from 'lucide-react';
import { useCookieConsent, COOKIE_CATEGORIES, type CookieConsent } from '@/lib/cookie-consent';
import Link from 'next/link';

export function CookieConsentBanner() {
  const { 
    showBanner, 
    consent, 
    acceptAll, 
    rejectAll, 
    updateConsent, 
    hideBanner 
  } = useCookieConsent();
  
  const [showSettings, setShowSettings] = useState(false);
  const [tempConsent, setTempConsent] = useState<Partial<CookieConsent>>({
    functional: consent?.functional ?? false,
    analytics: consent?.analytics ?? false
  });

  if (!showBanner) return null;

  const handleSaveSettings = () => {
    updateConsent(tempConsent);
    setShowSettings(false);
  };

  const handleShowSettings = () => {
    setTempConsent({
      functional: consent?.functional ?? false,
      analytics: consent?.analytics ?? false
    });
    setShowSettings(true);
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'essential': return <Shield className="h-4 w-4" />;
      case 'functional': return <Wrench className="h-4 w-4" />;
      case 'analytics': return <BarChart3 className="h-4 w-4" />;
      default: return <Cookie className="h-4 w-4" />;
    }
  };

  return (
    <>
      {/* Main Banner */}
      <div className="fixed bottom-0 left-0 right-0 z-50 p-4 bg-background/95 backdrop-blur-sm border-t shadow-lg">
        <div className="max-w-6xl mx-auto">
          <Card>
            <CardContent className="p-6">
              <div className="flex flex-col lg:flex-row lg:items-center gap-4">
                <div className="flex items-start gap-3 flex-1">
                  <Cookie className="h-6 w-6 text-primary mt-1 flex-shrink-0" />
                  <div className="space-y-2">
                    <h3 className="font-semibold text-lg">We use cookies to enhance your experience</h3>
                    <p className="text-sm text-muted-foreground">
                      We use essential cookies for functionality and optional cookies for analytics to improve our service. 
                      You can customize your preferences or accept all cookies.
                    </p>
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <span>Learn more in our</span>
                      <Link 
                        href="/legal/privacy" 
                        className="text-primary hover:underline"
                        target="_blank"
                      >
                        Privacy Policy
                      </Link>
                      <span>and</span>
                      <Link 
                        href="/legal/cookies" 
                        className="text-primary hover:underline"
                        target="_blank"
                      >
                        Cookie Policy
                      </Link>
                    </div>
                  </div>
                </div>
                
                <div className="flex flex-col sm:flex-row gap-2 lg:flex-shrink-0">
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={handleShowSettings}
                    className="flex items-center gap-2"
                  >
                    <Settings className="h-4 w-4" />
                    Customize
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={rejectAll}
                  >
                    Reject All
                  </Button>
                  <Button 
                    size="sm"
                    onClick={acceptAll}
                    className="bg-primary text-primary-foreground hover:bg-primary/90"
                  >
                    Accept All
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Settings Dialog */}
      <Dialog open={showSettings} onOpenChange={setShowSettings}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Cookie className="h-5 w-5" />
              Cookie Preferences
            </DialogTitle>
            <DialogDescription>
              Choose which cookies you want to allow. Essential cookies are required for the website to function.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-6 py-4">
            {Object.entries(COOKIE_CATEGORIES).map(([key, category]) => (
              <Card key={key}>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {getCategoryIcon(key)}
                      <CardTitle className="text-base">{category.name}</CardTitle>
                      {category.required && (
                        <Badge variant="secondary" className="text-xs">
                          Required
                        </Badge>
                      )}
                    </div>
                    <Switch
                      checked={
                        key === 'essential' 
                          ? true 
                          : tempConsent[key as keyof typeof tempConsent] ?? false
                      }
                      onCheckedChange={(checked) => {
                        if (key !== 'essential') {
                          setTempConsent(prev => ({
                            ...prev,
                            [key]: checked
                          }));
                        }
                      }}
                      disabled={key === 'essential'}
                    />
                  </div>
                  <CardDescription className="text-sm">
                    {category.description}
                  </CardDescription>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="space-y-1">
                    <Label className="text-xs font-medium text-muted-foreground">
                      Examples:
                    </Label>
                    <ul className="text-xs text-muted-foreground space-y-1">
                      {category.cookies.map((cookie, index) => (
                        <li key={index} className="flex items-center gap-2">
                          <div className="w-1 h-1 bg-muted-foreground rounded-full" />
                          {cookie}
                        </li>
                      ))}
                    </ul>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
          
          <div className="flex flex-col sm:flex-row gap-2 pt-4">
            <Button 
              variant="outline" 
              onClick={() => setShowSettings(false)}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button 
              onClick={() => {
                setTempConsent({ functional: false, analytics: false });
              }}
              variant="outline"
              className="flex-1"
            >
              Reject All
            </Button>
            <Button 
              onClick={() => {
                setTempConsent({ functional: true, analytics: true });
              }}
              variant="outline"
              className="flex-1"
            >
              Accept All
            </Button>
            <Button 
              onClick={handleSaveSettings}
              className="flex-1"
            >
              Save Preferences
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
