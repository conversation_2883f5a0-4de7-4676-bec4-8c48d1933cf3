import { AuthProvider } from '@/lib/auth-context'
import { SubscriptionProvider } from '@/lib/subscription-context'
import { CookieConsentProvider } from '@/lib/cookie-consent'
import { CookieConsentBanner } from '@/components/CookieConsentBanner'
import { Inter } from 'next/font/google'

const inter = Inter({
  variable: '--font-inter',
  subsets: ['latin'],
  display: 'swap',
})

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <CookieConsentProvider>
      <AuthProvider>
        <SubscriptionProvider>
          <div className={`${inter.variable} font-sans antialiased h-full bg-background text-foreground`}>
            {children}
            <CookieConsentBanner />
          </div>
        </SubscriptionProvider>
      </AuthProvider>
    </CookieConsentProvider>
  )
}