"use client";

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Download, Loader2, Shield, Info, Clock } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/lib/auth-context';
import { logger } from '@/lib/logger';

interface ExportedData {
  exportInfo: {
    exportedAt: string;
    userId: string;
    version: string;
    format: string;
  };
  profile: any;
  settings: any;
  flashcardSets: any[];
  usage: any;
  subscription?: any;
}

export function DataExportSection() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [isExporting, setIsExporting] = useState(false);
  const [lastExportTime, setLastExportTime] = useState<string | null>(null);

  const handleExportData = async () => {
    if (!user) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "You must be logged in to export your data.",
      });
      return;
    }

    setIsExporting(true);
    
    try {
      logger.log('[Data Export] Starting export for user:', user.id);
      
      const response = await fetch('/api/user/export-data', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: user.id
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();

        if (response.status === 429) {
          // Rate limit exceeded
          const resetTime = errorData.resetTime ? new Date(errorData.resetTime).toLocaleString() : 'later';
          toast({
            variant: "destructive",
            title: "Export Limit Reached",
            description: `You can only export your data 3 times per hour. Please try again at ${resetTime}.`,
          });
          return;
        }

        if (response.status === 503 && errorData.code === 'SERVICE_UNAVAILABLE') {
          // Service unavailable (missing Firebase Admin credentials)
          toast({
            variant: "destructive",
            title: "Service Temporarily Unavailable",
            description: "The data export service is currently unavailable. Please contact support for assistance with downloading your data.",
          });
          return;
        }

        throw new Error(errorData.error || 'Export failed');
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Export failed');
      }

      // Create and download the file
      const exportData: ExportedData = result.data;
      const dataStr = JSON.stringify(exportData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      
      // Create download link
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `flash-cards-ai-data-export-${new Date().toISOString().split('T')[0]}.json`;
      
      // Trigger download
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      // Clean up
      URL.revokeObjectURL(url);
      
      // Update last export time
      setLastExportTime(new Date().toISOString());
      
      toast({
        title: "Export Complete",
        description: `Your data has been exported successfully. Downloaded ${exportData.flashcardSets.length} flashcard sets.`,
      });
      
      logger.log('[Data Export] Export completed successfully:', {
        userId: user.id,
        flashcardSetsCount: exportData.flashcardSets.length,
        totalFlashcards: exportData.flashcardSets.reduce((sum, set) => sum + set.flashcardCount, 0)
      });
      
    } catch (error) {
      logger.error('[Data Export] Export failed:', error);
      toast({
        variant: "destructive",
        title: "Export Failed",
        description: error instanceof Error ? error.message : "Failed to export your data. Please try again.",
      });
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Download className="h-5 w-5" />
          Download My Data
        </CardTitle>
        <CardDescription>
          Export all your personal data in JSON format (GDPR Article 20 - Data Portability)
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            This export includes your profile information, settings, all flashcard sets, usage statistics, 
            and subscription details. API keys are not included for security reasons.
          </AlertDescription>
        </Alert>

        <div className="space-y-3">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Shield className="h-4 w-4" />
            <span>Your data is exported securely and directly to your device</span>
          </div>
          
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Clock className="h-4 w-4" />
            <span>Export limit: 3 requests per hour for security</span>
          </div>

          {lastExportTime && (
            <div className="text-sm text-muted-foreground">
              Last export: {new Date(lastExportTime).toLocaleString()}
            </div>
          )}
        </div>

        <div className="pt-2">
          <Button 
            onClick={handleExportData} 
            disabled={isExporting}
            className="w-full sm:w-auto"
          >
            {isExporting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Exporting Data...
              </>
            ) : (
              <>
                <Download className="mr-2 h-4 w-4" />
                Download My Data
              </>
            )}
          </Button>
        </div>

        <div className="text-xs text-muted-foreground pt-2 border-t">
          <p>
            <strong>What's included:</strong> Profile information, flashcard sets, settings, usage statistics, subscription details
          </p>
          <p className="mt-1">
            <strong>What's excluded:</strong> API keys, payment information, temporary session data
          </p>
          <p className="mt-1">
            <strong>Format:</strong> JSON file compatible with most data processing tools
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
