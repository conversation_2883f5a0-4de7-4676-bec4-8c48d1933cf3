'use client'

import { usePathname, useSearchParams } from 'next/navigation'
import Script from 'next/script'
import { useEffect, useState, Suspense } from 'react'
import { Analytics as VercelAnalytics } from '@vercel/analytics/react'
import { hasAnalyticsConsent } from '@/lib/cookie-consent'
import { logger } from '@/lib/logger'

function AnalyticsContent() {
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const [hasConsent, setHasConsent] = useState(false)
  const [isLoaded, setIsLoaded] = useState(false)

  useEffect(() => {
    // Check initial consent
    const checkConsent = () => {
      const consent = hasAnalyticsConsent()
      setHasConsent(consent)
      setIsLoaded(true)
      logger.log('Analytics consent status:', consent)
    }

    checkConsent()

    // Listen for consent changes
    const handleConsentChange = (event: CustomEvent) => {
      const newConsent = event.detail.analytics
      setHasConsent(newConsent)
      logger.log('Analytics consent changed:', newConsent)

      if (newConsent && window.gtag) {
        // Re-initialize analytics if consent was granted
        window.gtag('config', process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID!, {
          page_path: window.location.pathname,
        })
      }
    }

    window.addEventListener('cookieConsentChanged', handleConsentChange as EventListener)

    return () => {
      window.removeEventListener('cookieConsentChanged', handleConsentChange as EventListener)
    }
  }, [])

  useEffect(() => {
    if (hasConsent && window.gtag && process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID) {
      const url = pathname + searchParams.toString()
      window.gtag('config', process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID, {
        page_path: url,
      })
    }
  }, [pathname, searchParams, hasConsent])

  // Only load analytics scripts if user has consented and GA ID is configured
  if (!hasConsent || !isLoaded || !process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID) {
    return null
  }

  return (
    <>
      <Script
        strategy="afterInteractive"
        src={`https://www.googletagmanager.com/gtag/js?id=${process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID}`}
      />
      <Script
        id="google-analytics"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '${process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID}', {
              page_path: window.location.pathname,
            });
          `,
        }}
      />
    </>
  )
}

export function Analytics() {
  const [hasConsent, setHasConsent] = useState(false)
  const [isLoaded, setIsLoaded] = useState(false)

  useEffect(() => {
    const checkConsent = () => {
      const consent = hasAnalyticsConsent()
      setHasConsent(consent)
      setIsLoaded(true)
    }

    checkConsent()

    const handleConsentChange = (event: CustomEvent) => {
      setHasConsent(event.detail.analytics)
    }

    window.addEventListener('cookieConsentChanged', handleConsentChange as EventListener)

    return () => {
      window.removeEventListener('cookieConsentChanged', handleConsentChange as EventListener)
    }
  }, [])

  return (
    <Suspense fallback={null}>
      <AnalyticsContent />
      {/* Only load Vercel Analytics if user has consented */}
      {hasConsent && isLoaded && <VercelAnalytics />}
    </Suspense>
  )
}