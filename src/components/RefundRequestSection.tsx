"use client";

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { RefreshCw, Loader2, AlertCircle, CheckCircle, Clock, XCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/lib/auth-context';
import { useSubscription } from '@/hooks/use-subscription';
import { logger } from '@/lib/logger';

interface RefundRequest {
  id: string;
  reason: string;
  description: string;
  requestedAmount: number;
  status: 'pending' | 'approved' | 'denied' | 'processed';
  createdAt: string;
  processedAt?: string;
  adminNotes?: string;
}

const REFUND_REASONS = [
  { value: 'service_unavailable', label: 'Service Unavailable (48+ hours)' },
  { value: 'billing_error', label: 'Billing Error or Duplicate Charge' },
  { value: 'early_cancellation', label: 'Early Cancellation (within 7 days)' },
  { value: 'technical_issues', label: 'Technical Issues (7+ days)' },
  { value: 'other', label: 'Other (please specify)' }
];

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'pending':
      return <Clock className="h-4 w-4 text-yellow-500" />;
    case 'approved':
      return <CheckCircle className="h-4 w-4 text-green-500" />;
    case 'processed':
      return <CheckCircle className="h-4 w-4 text-green-600" />;
    case 'denied':
      return <XCircle className="h-4 w-4 text-red-500" />;
    default:
      return <Clock className="h-4 w-4 text-gray-500" />;
  }
};

const getStatusLabel = (status: string) => {
  switch (status) {
    case 'pending':
      return 'Under Review';
    case 'approved':
      return 'Approved';
    case 'processed':
      return 'Refund Processed';
    case 'denied':
      return 'Denied';
    default:
      return status;
  }
};

export function RefundRequestSection() {
  const { user } = useAuth();
  const { subscriptionsEnabled, subscription } = useSubscription();
  const { toast } = useToast();
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [refundRequests, setRefundRequests] = useState<RefundRequest[]>([]);
  
  // Form state
  const [reason, setReason] = useState('');
  const [description, setDescription] = useState('');
  const [paymentReference, setPaymentReference] = useState('');

  useEffect(() => {
    if (user && subscriptionsEnabled) {
      loadRefundRequests();
    }
  }, [user, subscriptionsEnabled]);

  const loadRefundRequests = async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      const response = await fetch(`/api/user/refund-request?userId=${user.id}`);
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setRefundRequests(result.refundRequests);
        }
      }
    } catch (error) {
      logger.error('[Refund Request] Error loading refund requests:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmitRefund = async () => {
    if (!user) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "You must be logged in to submit a refund request.",
      });
      return;
    }

    if (!reason || !description.trim()) {
      toast({
        variant: "destructive",
        title: "Missing Information",
        description: "Please select a reason and provide a description for your refund request.",
      });
      return;
    }

    setIsSubmitting(true);
    
    try {
      const response = await fetch('/api/user/refund-request', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: user.id,
          reason,
          description: description.trim(),
          paymentReference: paymentReference.trim() || undefined
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        if (response.status === 429) {
          toast({
            variant: "destructive",
            title: "Request Limit Reached",
            description: result.error || "You can only submit 3 refund requests per day.",
          });
          return;
        }

        if (response.status === 503 && result.code === 'SERVICE_UNAVAILABLE') {
          toast({
            variant: "destructive",
            title: "Service Temporarily Unavailable",
            description: "The refund request service is currently unavailable. Please contact support directly for refund assistance.",
          });
          return;
        }

        throw new Error(result.error || 'Failed to submit refund request');
      }

      if (result.success) {
        toast({
          title: "Refund Request Submitted",
          description: result.message,
        });
        
        // Reset form
        setReason('');
        setDescription('');
        setPaymentReference('');
        setShowForm(false);
        
        // Reload refund requests
        await loadRefundRequests();
      } else {
        throw new Error(result.error || 'Failed to submit refund request');
      }
      
    } catch (error) {
      logger.error('[Refund Request] Error submitting refund request:', error);
      toast({
        variant: "destructive",
        title: "Submission Failed",
        description: error instanceof Error ? error.message : "Failed to submit refund request. Please try again.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Don't show if subscriptions are not enabled or user has no subscription
  if (!subscriptionsEnabled || !subscription || subscription.tier === 'FREE') {
    return null;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <RefreshCw className="h-5 w-5" />
          Refund Requests
        </CardTitle>
        <CardDescription>
          Request a refund for your subscription if you meet our refund policy criteria
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Refunds are available for service unavailability (48+ hours), billing errors, early cancellation (within 7 days with minimal usage), 
            or technical issues (7+ days). See our Terms & Conditions for full refund policy details.
          </AlertDescription>
        </Alert>

        {/* Existing Refund Requests */}
        {refundRequests.length > 0 && (
          <div className="space-y-3">
            <h4 className="font-medium">Your Refund Requests</h4>
            {refundRequests.map((request) => (
              <div key={request.id} className="border rounded-lg p-3 space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {getStatusIcon(request.status)}
                    <span className="font-medium">{getStatusLabel(request.status)}</span>
                  </div>
                  <span className="text-sm text-muted-foreground">
                    {new Date(request.createdAt).toLocaleDateString()}
                  </span>
                </div>
                <p className="text-sm text-muted-foreground">{request.description}</p>
                {request.requestedAmount > 0 && (
                  <p className="text-sm">Amount: R{request.requestedAmount}</p>
                )}
                {request.adminNotes && (
                  <div className="bg-muted p-2 rounded text-sm">
                    <strong>Admin Notes:</strong> {request.adminNotes}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}

        {/* New Refund Request Form */}
        {!showForm ? (
          <Button 
            onClick={() => setShowForm(true)}
            variant="outline"
            className="w-full"
          >
            Submit New Refund Request
          </Button>
        ) : (
          <div className="space-y-4 border rounded-lg p-4">
            <h4 className="font-medium">New Refund Request</h4>
            
            <div className="space-y-2">
              <Label htmlFor="refund-reason">Reason for Refund</Label>
              <Select value={reason} onValueChange={setReason}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a reason" />
                </SelectTrigger>
                <SelectContent>
                  {REFUND_REASONS.map((reasonOption) => (
                    <SelectItem key={reasonOption.value} value={reasonOption.value}>
                      {reasonOption.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="refund-description">Description</Label>
              <Textarea
                id="refund-description"
                placeholder="Please provide detailed information about your refund request, including dates, specific issues encountered, and any relevant details..."
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                rows={4}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="payment-reference">Payment Reference (Optional)</Label>
              <Input
                id="payment-reference"
                placeholder="Payment reference number or transaction ID"
                value={paymentReference}
                onChange={(e) => setPaymentReference(e.target.value)}
              />
            </div>

            <div className="flex gap-2">
              <Button 
                onClick={handleSubmitRefund} 
                disabled={isSubmitting}
                className="flex-1"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Submitting...
                  </>
                ) : (
                  'Submit Request'
                )}
              </Button>
              <Button 
                variant="outline" 
                onClick={() => setShowForm(false)}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
            </div>
          </div>
        )}

        <div className="text-xs text-muted-foreground pt-2 border-t">
          <p>
            <strong>Review Process:</strong> Refund requests are reviewed within 5 business days. 
            Approved refunds are processed within 7-14 business days to your original payment method.
          </p>
          <p className="mt-1">
            <strong>Contact:</strong> For urgent refund matters, email <EMAIL>
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
