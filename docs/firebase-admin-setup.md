# Firebase Admin SDK Configuration

## Overview

The Flash Cards AI application requires Firebase Admin SDK credentials to perform server-side operations such as:
- Data export functionality (GDPR compliance)
- Refund request processing
- User settings management
- Server-side user authentication

## Current Issue

The application is currently missing Firebase Admin service account credentials, which causes the following errors:
```
Service account object must contain a string 'private_key' property
```

## Required Environment Variables

You need to add Firebase Admin service account credentials to your `.env` file. There are two ways to configure this:

### Option 1: Individual Environment Variables (Recommended)

Add these variables to your `.env` file:

```env
FIREBASE_ADMIN_TYPE=service_account
FIREBASE_ADMIN_PROJECT_ID=your-project-id
FIREBASE_ADMIN_PRIVATE_KEY_ID=your-private-key-id
FIREBASE_ADMIN_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour-Private-Key-Here\n-----END PRIVATE KEY-----"
FIREBASE_ADMIN_CLIENT_EMAIL=<EMAIL>
FIREBASE_ADMIN_CLIENT_ID=your-client-id
FIREBASE_ADMIN_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_ADMIN_TOKEN_URI=https://oauth2.googleapis.com/token
FIREBASE_ADMIN_AUTH_PROVIDER_X509_CERT_URL=https://www.googleapis.com/oauth2/v1/certs
FIREBASE_ADMIN_CLIENT_X509_CERT_URL=https://www.googleapis.com/robot/v1/metadata/x509/your-service-account%40your-project.iam.gserviceaccount.com
```

### Option 2: JSON Service Account (Alternative)

Alternatively, you can use a single JSON environment variable:

```env
FIREBASE_ADMIN_SERVICE_ACCOUNT_JSON='{"type":"service_account","project_id":"your-project-id",...}'
```

## How to Get Firebase Admin Credentials

1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Select your project
3. Go to Project Settings (gear icon)
4. Navigate to the "Service accounts" tab
5. Click "Generate new private key"
6. Download the JSON file
7. Use the values from this JSON file to populate the environment variables above

## Security Notes

- **Never commit service account credentials to version control**
- Add `.env` to your `.gitignore` file
- Use different service accounts for development and production
- Regularly rotate service account keys

## Testing the Configuration

After adding the credentials, restart your development server and test:

1. Go to Settings page
2. Try the "Download My Data" feature
3. Try submitting a refund request

If configured correctly, these features should work without the "Service account object must contain a string 'private_key' property" error.

## Graceful Degradation

The application has been updated to handle missing Firebase Admin credentials gracefully:
- Users will see a "Service Temporarily Unavailable" message
- The application won't crash
- Other features that don't require Firebase Admin will continue to work

## Development vs Production

For development, you can use a development Firebase project with its own service account.
For production, ensure you use production Firebase credentials and follow security best practices.
